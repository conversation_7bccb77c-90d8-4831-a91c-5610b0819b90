import React from 'react';
import { Button } from 'antd';

const SiteMapSideBar = ({ onFilterChange, activeFilters }) => {
    const handleFilterChange = (filterKey, value) => {
        const newFilters = {
            ...activeFilters,
            [filterKey]: value,
        };
        onFilterChange(newFilters);
    };

    const clearFilters = () => {
        onFilterChange({});
    };

    return (
        <div className="gx-module-side-content">
            <div className="gx-module-side-header">
                <div className="gx-module-side-header-content">
                    <h2 className="gx-module-side-title">Site Map Filters</h2>
                </div>
            </div>

            <div className="gx-module-side-content-inner gx-p-4">
                {/* Filter Section */}
                <div className="gx-mb-4">
                    <h4 className="gx-mb-3">Filters</h4>
                    
                    {/* Status Filter */}
                    <div className="gx-mb-3">
                        <label className="gx-form-label">Status</label>
                        <select
                            className="gx-form-control"
                            value={activeFilters.status || ''}
                            onChange={(e) => handleFilterChange('status', e.target.value)}
                        >
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    {/* Site Type Filter */}
                    <div className="gx-mb-3">
                        <label className="gx-form-label">Site Type</label>
                        <select
                            className="gx-form-control"
                            value={activeFilters.site_type || ''}
                            onChange={(e) => handleFilterChange('site_type', e.target.value)}
                        >
                            <option value="">All Types</option>
                            <option value="warehouse">Warehouse</option>
                            <option value="office">Office</option>
                            <option value="service_center">Service Center</option>
                        </select>
                    </div>

                    {/* City Filter */}
                    <div className="gx-mb-3">
                        <label className="gx-form-label">City</label>
                        <select
                            className="gx-form-control"
                            value={activeFilters.city || ''}
                            onChange={(e) => handleFilterChange('city', e.target.value)}
                        >
                            <option value="">All Cities</option>
                            <option value="delhi">Delhi</option>
                            <option value="mumbai">Mumbai</option>
                            <option value="bangalore">Bangalore</option>
                            <option value="chennai">Chennai</option>
                            <option value="kolkata">Kolkata</option>
                            <option value="hyderabad">Hyderabad</option>
                        </select>
                    </div>

                    {/* Clear Filters Button */}
                    <Button 
                        type="default" 
                        onClick={clearFilters}
                        className="gx-w-100"
                        disabled={Object.keys(activeFilters).length === 0}
                    >
                        Clear Filters
                    </Button>
                </div>

                {/* Info Section */}
                <div className="gx-mb-4">
                    <h4 className="gx-mb-3">Map Info</h4>
                    <div className="gx-text-muted gx-text-sm">
                        <p>• Click on markers to view site details</p>
                        <p>• Use search to find specific sites</p>
                        <p>• Switch between marker and heat map views</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SiteMapSideBar;
