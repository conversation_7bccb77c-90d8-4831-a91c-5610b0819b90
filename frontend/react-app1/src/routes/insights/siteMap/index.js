import React, { useState } from 'react';
import { Drawer } from 'antd';
import CustomScrollbars from '../../../util/CustomScrollbars';
import SiteMapOverview from './SiteMapOverview';
import AppModuleHeader from '../../../components/AppModuleHeader';
import SiteMapSideBar from './SiteMapSideBar';

const SiteMap = () => {
    const [filterConfig, setFilterConfig] = useState({
        activeFilters: {},
        searchFilter: '',
        drawerState: false,
    });

    const getExportMenuData = () => {
        return [
            {
                key: 'export-csv',
                label: 'Export CSV',
                icon: 'icon-export',
                onClick: () => {
                    console.log('Export CSV clicked');
                    // TODO: Implement CSV export functionality
                },
            },
            {
                key: 'export-pdf',
                label: 'Export PDF',
                icon: 'icon-pdf',
                onClick: () => {
                    console.log('Export PDF clicked');
                    // TODO: Implement PDF export functionality
                },
            },
        ];
    };

    return (
        <div className="gx-main-content">
            <div className="gx-app-module">
                {/* Mobile Drawer */}
                <div className="gx-d-block gx-d-lg-none">
                    <Drawer
                        placement="left"
                        closable={false}
                        visible={filterConfig.drawerState}
                        onClose={() =>
                            setFilterConfig((prevConfig) => ({
                                ...prevConfig,
                                drawerState: false,
                            }))
                        }
                    >
                        <SiteMapSideBar
                            onFilterChange={(newFilters) =>
                                setFilterConfig((prev) => ({
                                    ...prev,
                                    activeFilters: newFilters,
                                }))
                            }
                            activeFilters={filterConfig.activeFilters}
                        />
                    </Drawer>
                </div>

                {/* Desktop Sidebar */}
                <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                    <SiteMapSideBar
                        onFilterChange={(newFilters) =>
                            setFilterConfig((prev) => ({
                                ...prev,
                                activeFilters: newFilters,
                            }))
                        }
                        activeFilters={filterConfig.activeFilters}
                    />
                </div>

                {/* Main Content */}
                <div className="gx-module-box">
                    <div className="gx-module-box-header">
                        <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                            <i
                                className="icon icon-filter gx-icon-btn"
                                aria-label="Menu"
                                onClick={() =>
                                    setFilterConfig((prevConfig) => ({
                                        ...prevConfig,
                                        drawerState: true,
                                    }))
                                }
                            />
                        </span>

                        <AppModuleHeader
                            placeholder="Search by Site ID, Pincode..."
                            currValue={filterConfig.searchFilter}
                            optionsMenuData={getExportMenuData()}
                            onChange={(value) =>
                                setFilterConfig((prevConfig) => ({
                                    ...prevConfig,
                                    searchFilter: value,
                                }))
                            }
                        />
                    </div>

                    <div className="gx-module-box-content gx-px-4 gx-py-3">
                        <div className="gx-d-none gx-d-lg-block gx-h-100">
                            <CustomScrollbars>
                                <SiteMapOverview
                                    filterObject={filterConfig.activeFilters}
                                    searchQuery={filterConfig.searchFilter}
                                />
                            </CustomScrollbars>
                        </div>
                        <div className="gx-d-lg-none gx-d-block gx-h-100">
                            <SiteMapOverview
                                filterObject={filterConfig.activeFilters}
                                searchQuery={filterConfig.searchFilter}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SiteMap;
